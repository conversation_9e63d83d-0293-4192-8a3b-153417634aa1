const allowedOrigins = require('./AllowedOrigins');

const corsOptions = {
    origin: (origin, callback) => {
        console.log('CORS Origin:', origin); // Debug log
        
        // Allow requests with no origin (mobile apps, postman, etc.)
        if (!origin) {
            return callback(null, true);
        }
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            console.log('CORS Error - Origin not allowed:', origin);
            console.log('Allowed origins:', allowedOrigins);
            callback(new Error("Not allowed by CORS"));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    optionsSuccessStatus: 200
};

module.exports = corsOptions;