import 'dart:convert';
import 'package:http/http.dart' as http;
import 'models.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

const String MyLink = 'https://cust-api.albreeqshipping.com';
final Map<String, String> headers = {"Access-Control-Allow-Origin": "*"};

Future<LoginAttempt> Login(String userName, String password) async {
  const url = '$MyLink/auth/login';
  final body = jsonEncode({
    'userName': userName,
    'password': password,
  });
  try {
    final response = await http
        .post(Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
            },
            body: body)
        .timeout(const Duration(seconds: 7), onTimeout: () {
      print('TIMEOUT TIMEO');
      return http.Response(
        jsonEncode({'error': 'timeout'}),
        408,
      );
    });

    if (response.statusCode == 408) {
      return LoginAttempt(
          407,
          LoginResponse(
              isAuth: false,
              id: 1,
              accessToken: 'accessToken',
              refreshToken: 'refreshToken',
              msg: 'فشل تسجيل الدخول'));
    }
    print(response.body);

    if (response.statusCode == 200) {
      var jsonResponse = jsonDecode(response.body);
      if (jsonResponse['IsAuth'] != null) {
        if (jsonResponse['IsAuth'] == true) {
          storeToken(jsonResponse['accessToken'] ?? '', userName, password,
              jsonResponse['ID'] ?? 1);
        }
      }
      print(jsonResponse['IsAuth']);
      return LoginAttempt(
          response.statusCode, LoginResponse.FromJSON(jsonResponse));
    } else {
      return LoginAttempt(
          response.statusCode,
          LoginResponse(
              isAuth: false,
              id: 1,
              accessToken: 'accessToken',
              refreshToken: 'refreshToken',
              msg: 'فشل تسجيل الدخول'));
    }
  } on Exception catch (e) {
    print(e);
    return LoginAttempt(
        444,
        LoginResponse(
            isAuth: false,
            id: 1,
            accessToken: 'accessToken',
            refreshToken: 'refreshToken',
            msg: 'لا يوجد اتصال بالانترنت'));
  }
}

Future<void> storeToken(
    String token, String username, String pass, int Id) async {
  const storage = FlutterSecureStorage();
  await storage.write(key: 'token', value: token);
  await storage.write(key: 'username', value: username);
  await storage.write(key: 'pass', value: pass);
  await storage.write(key: 'id', value: Id.toString());
  await storage.write(key: 'refreshToken', value: refreshToken.toString());
}

class LoginInfo {
  final String userName;
  final String phoneNumber;
  final String cusNum;
  final int orderCount;
  LoginInfo(
      {required this.userName,
      required this.phoneNumber,
      required this.cusNum,
      required this.orderCount});

  factory LoginInfo.FromJSON(Map<String, dynamic> json) {
    return LoginInfo(
      userName: json['userName'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      cusNum: json['CusNum'] ?? '',
      orderCount: json['OrderCount'] ?? 0,
    );
  }
}

Future<LoginInfo> GetInfo() async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/home';
  String? token = await storage.read(key: 'token');
  String? Id = await storage.read(key: 'id');

  try {
    final body = jsonEncode({
      'ID': Id,
    });
    var response = await http
        .post(Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: body)
        .timeout(const Duration(seconds: 7), onTimeout: () {
      print('TIMEOUT TIMEO');
      return http.Response(
        jsonEncode({'error': 'timeout'}),
        408,
      );
    });
    if (response.statusCode == 403) {
      switch (await refreshToken()) {
        case 0:
          response = await http
              .post(Uri.parse(url),
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer $token',
                  },
                  body: body)
              .timeout(const Duration(seconds: 7), onTimeout: () {
            print('TIMEOUT TIMEO');
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          });
          break;
        case 1:
          return LoginInfo(
              userName: 'err', phoneNumber: "0", cusNum: "0", orderCount: 0);
        case 2:
          return LoginInfo(
              userName: '_logout',
              phoneNumber: "0",
              cusNum: "0",
              orderCount: 0);
        default:
          return LoginInfo(
              userName: 'err', phoneNumber: "0", cusNum: "0", orderCount: 0);
      }
    }
    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return LoginInfo(
        userName: json['Name'],
        phoneNumber: json['Phone'],
        cusNum: json['cusNum'],
        orderCount: json['OrderCount'],
      );
    } else {
      if (response.statusCode == 403) {
        return LoginInfo(
            userName: '_logout', phoneNumber: "0", cusNum: "0", orderCount: 0);
      }
    }
  } catch (e) {
    return LoginInfo(
        userName: 'err', phoneNumber: "-", cusNum: "-", orderCount: 0);
  }
  if (token == null) {
    return LoginInfo(
        userName: 'goBack',
        phoneNumber: "NonReal",
        cusNum: "NonReal",
        orderCount: 0);
  }
  return LoginInfo(
      userName: 'err', phoneNumber: "-", cusNum: "-", orderCount: 0);
}

Future<int> refreshToken() async {
  const storage = FlutterSecureStorage();
  String? refreshToken = await storage.read(key: 'refreshToken');
  if (refreshToken == null) {
    return 1;
  }
  await http.post(
    Uri.parse('$MyLink/auth/refresh'),
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $refreshToken',
    },
  ).then(
    (value) {
      if (value.statusCode == 200) {
        final json = jsonDecode(value.body);
        storage.write(key: 'token', value: json['accessToken']);
        storage.write(key: 'refreshToken', value: json['refreshToken']);
        return 0;
      } else {
        if (value.statusCode == 500) {
          return 2;
        }
      }
      return 1;
    },
  );
  return 2;
}

Future<void> SaveUserPro(LoginInfo usrinfo) async {
  const FlutterSecureStorage storage = FlutterSecureStorage();

  await storage.write(key: "UserPro", value: jsonEncode(usrinfo));
}

Future<LoginInfo> GetUserPro() async {
  const storage = FlutterSecureStorage();
  String? value = await storage.read(key: 'UserPro');
  if (value == null) {
    GetInfo().then((value) {
      SaveUserPro(value);
    });
  }
  return LoginInfo.FromJSON(jsonDecode(value!));
}

// Updated GetOrders function with pagination support
Future<OrdersResponse> GetOrders({
  int page = 1,
  int limit = 10,
  String search = '',
  int? isDelivered,
}) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/orders/get';
  String? token = await storage.read(key: 'token');
  String? Id = await storage.read(key: 'id');

  OrdersResponse erroredRes = OrdersResponse(
    success: false,
    data: OrdersData(
      orders: [
        OrderResponse(
          orderId: 0,
          orderNum: 'ERROR',
          numPackages: 0,
          numCbm: 0,
          priceLD: 0.0,
          totePriceLD: 0.0,
          orderTypeId: 0,
          isDelivered: false,
          status: '',
          orderType: '',
          unit: '',
          statusNum: 0,
        ),
      ],
      pagination: PaginationInfo(
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10,
        hasNextPage: false,
        hasPrevPage: false,
      ),
    ),
    msg: 'خطأ في الاتصال',
  );

  OrdersResponse logoutRes = OrdersResponse(
    success: false,
    data: OrdersData(
      orders: [
        OrderResponse(
          orderId: 0,
          orderNum: 'LOGIN',
          numPackages: 0,
          numCbm: 0,
          priceLD: 0.0,
          totePriceLD: 0.0,
          orderTypeId: 0,
          isDelivered: false,
          status: '',
          orderType: '',
          unit: '',
          statusNum: 0,
        ),
      ],
      pagination: PaginationInfo(
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10,
        hasNextPage: false,
        hasPrevPage: false,
      ),
    ),
    msg: 'يرجى تسجيل الدخول',
  );

  if (token == null) {
    return logoutRes;
  }

  try {
    final body = jsonEncode({
      'CustomerID': int.parse(Id!),
      'page': page,
      'limit': limit,
      'search': search,
      if (isDelivered != null) 'IsDelvierid': isDelivered,
    });

    final response = await http
        .post(Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: body)
        .timeout(const Duration(seconds: 7), onTimeout: () {
      print('TIMEOUT TIMEO');
      return http.Response(
        jsonEncode({'error': 'timeout'}),
        408,
      );
    });

    switch (response.statusCode) {
      case 401:
        return OrdersResponse(
          success: false,
          data: erroredRes.data,
          msg: 'غير مصرح بالوصول',
        );
      case 403:
        await refreshToken();
        return OrdersResponse(
          success: false,
          data: erroredRes.data,
          msg: 'انتهت صلاحية الجلسة',
        );
      case 200:
        try {
          final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
          return OrdersResponse.fromJson(jsonResponse);
        } catch (e) {
          print('Error parsing JSON response: $e');
          print('Response body: ${response.body}');
          return OrdersResponse(
            success: false,
            data: erroredRes.data,
            msg: 'خطأ في تحليل البيانات',
          );
        }
      default:
        print('Unexpected status code: ${response.statusCode}');
        print('Response body: ${response.body}');
        return OrdersResponse(
          success: false,
          data: erroredRes.data,
          msg: 'خطأ في الخادم (${response.statusCode})',
        );
    }
  } catch (e) {
    print('Error in GetOrders: $e');
    return erroredRes;
  }
}

Future<List<ShipmentResponse>> GetAccounts() async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/myaddress';
  String? token = await storage.read(key: 'token');
  String? Id = await storage.read(key: 'id');

  List<ShipmentResponse> erroredRes = [
    ShipmentResponse(
      userUrlId: 0,
      urlId: 0,
      urlTxt: '',
      status: '',
      insertDate: '',
      cityName: '',
      updateDate: '',
    ),
  ];

  try {
    final body = jsonEncode({
      'CusID': Id,
    });

    final response = await http
        .post(Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: body)
        .timeout(const Duration(seconds: 7), onTimeout: () {
      print('TIMEOUT TIMEO');
      return http.Response(
        jsonEncode({'error': 'timeout'}),
        408,
      );
    });

    if (response.statusCode == 401) {
      return erroredRes;
    }
    if (response.statusCode == 200) {
      final List<dynamic> jsonList = jsonDecode(response.body);

      List<ShipmentResponse> orders = jsonList
          .map((json) => ShipmentResponse(
                userUrlId: json['UserUrlID'] ?? 0,
                urlId: json['UrlID'] ?? 0,
                urlTxt: json['urlTxt'] ?? '',
                status: json['Status'] ?? '',
                insertDate: json['InsertDate'] ?? '',
                cityName: json['Url.CityName'] ?? '',
                updateDate: json['updateDate'] ?? '',
              ))
          .toList();

      return orders;
    }
  } catch (e) {
    return erroredRes;
  }

  if (token == null) {
    return erroredRes;
  }

  return erroredRes;
}

Future<List<CountriesWithCities>> GetCountriesAndCities() async {
  const storage = FlutterSecureStorage();
  const apiUrl = '$MyLink/myaddress/getUrls';
  String? token = await storage.read(key: 'token');

  List<CountriesWithCities> erroredRes = [
    CountriesWithCities(
      id: 0,
      contryId: 0,
      cityName: '',
      url: '',
      countryPrfix: '',
      countryName: '',
      status: 0,
    ),
  ];

  if (token == null) {
    return erroredRes;
  }

  try {
    final response = await http
        .post(
          Uri.parse(apiUrl),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        )
        .timeout(const Duration(seconds: 7), onTimeout: () {
      print('Request timeout');
      return http.Response(
        jsonEncode({'error': 'timeout'}),
        408,
      );
    });

    if (response.statusCode == 401) {
      print('Unauthorized access');
      return erroredRes;
    }

    if (response.statusCode != 200) {
      print('Error: ${response.statusCode}');
      return erroredRes;
    }

    final List<dynamic> locationsJson = jsonDecode(response.body);
    List<CountriesWithCities> locations = locationsJson
        .where((location) => location['Status'] == 1)
        .map<CountriesWithCities>((location) => CountriesWithCities(
              id: location['ID'] ?? 0,
              contryId: location['ContryID'] ?? 0,
              cityName: location['CityName'] ?? '',
              url: location['Url'] ?? '',
              countryPrfix: location['CountryPrfix'] ?? '',
              countryName: location['CountryName'] ?? '',
              status: location['Status'] ?? 0,
            ))
        .toList();

    return locations.isEmpty ? erroredRes : locations;
  } catch (e) {
    print('Error in GetCountriesAndCities: $e');
    return erroredRes;
  }
}

Future<List<TrackResponse>> trackOrder(int orderId) async {
  const storage = FlutterSecureStorage();
  final customerId = await storage.read(key: 'id');
  final token = await storage.read(key: 'token');

  if (customerId == null) {
    throw Exception('User ID not found in storage');
  }

  const url = '$MyLink/tracking';
  final body = jsonEncode({
    'CusID': int.parse(customerId),
    'OrderID': orderId,
  });

  try {
    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 408) {
      throw Exception('Request timed out');
    }

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      return jsonResponse.map((json) => TrackResponse.fromJson(json)).toList();
    } else {
      throw Exception('Failed to track order. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to track order: $e');
  }
}

// New API function to get order by ID
Future<OrderDetailResponse> getOrderById(int orderId) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/orders/GetOrderById';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final body = jsonEncode({
      'OrderID': orderId,
    });

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
      return OrderDetailResponse.fromJson(jsonResponse);
    } else {
      throw Exception('Failed to get order details. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get order details: $e');
  }
}

// New API function to get customer dashboard data
Future<DashboardResponse> getCustomerDashboard() async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/orders/GetCusDashBoard';
  String? token = await storage.read(key: 'token');
  String? customerId = await storage.read(key: 'id');

  if (token == null || customerId == null) {
    throw Exception('Authentication data not found');
  }

  try {
    final body = jsonEncode({
      'ID': int.parse(customerId),
    });

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
      return DashboardResponse.fromJson(jsonResponse);
    } else {
      throw Exception('Failed to get dashboard data. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get dashboard data: $e');
  }
}

// New API function to get countries list
Future<List<Country>> getCountries() async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/countries/getlist';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode({}),
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      return jsonResponse.map((json) => Country.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get countries. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get countries: $e');
  }
}

// New API function to get arrival locations
Future<List<ArrivalLocation>> getArrivalLocations(int typeId) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/arrive/getlist';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final body = jsonEncode({
      'typeID': typeId,
    });

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      return jsonResponse.map((json) => ArrivalLocation.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get arrival locations. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get arrival locations: $e');
  }
}

// Updated GetAccounts function to use new address endpoint
Future<List<AddressResponse>> getAddresses() async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/address';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final response = await http
        .get(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      return jsonResponse.map((json) => AddressResponse.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get addresses. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get addresses: $e');
  }
}

// Customer management API functions
Future<CustomerResponse> getCustomerById(int customerId) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/customer/getbyID';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final body = jsonEncode({
      'ID': customerId,
    });

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      if (jsonResponse.isNotEmpty) {
        return CustomerResponse.fromJson(jsonResponse.first);
      } else {
        throw Exception('Customer not found');
      }
    } else {
      throw Exception('Failed to get customer. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to get customer: $e');
  }
}

// Update customer profile
Future<bool> updateCustomer({
  required int customerId,
  String? custName,
  String? phoneNum1,
  String? phoneNum2,
  String? address,
  String? email,
  String? password,
}) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/customer/UpdateCus';
  String? token = await storage.read(key: 'token');
  String? userIdStr = await storage.read(key: 'id');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  if (userIdStr == null) {
    throw Exception('User ID not found');
  }

  try {
    // Build the body with the exact pattern required
    final Map<String, dynamic> bodyMap = {
      'ID': customerId,
      'UserID': int.parse(userIdStr),
    };

    // Only add non-null and non-empty values
    if (custName != null && custName.trim().isNotEmpty) {
      bodyMap['CustName'] = custName.trim();
    }

    if (phoneNum1 != null && phoneNum1.trim().isNotEmpty) {
      bodyMap['PhoneNum1'] = phoneNum1.trim();
    }

    if (phoneNum2 != null) {
      bodyMap['PhoneNum2'] = phoneNum2.trim();
    }

    if (address != null) {
      bodyMap['Address'] = address.trim();
    }

    if (email != null) {
      bodyMap['Email'] = email.trim();
    }

    if (password != null && password.trim().isNotEmpty) {
      bodyMap['Password'] = password.trim();
    }

    final body = jsonEncode(bodyMap);

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 201) {
      return true;
    } else {
      // Parse error response if available
      try {
        final errorResponse = jsonDecode(response.body);
        if (errorResponse is Map && errorResponse.containsKey('error')) {
          throw Exception(errorResponse['error']);
        }
      } catch (_) {
        // If parsing fails, use generic error
      }
      throw Exception('Failed to update customer. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to update customer: $e');
  }
}

// Generate new verification code for customer
Future<String> generateNewVerificationCode(int customerId) async {
  const storage = FlutterSecureStorage();
  const url = '$MyLink/customer/NewCusVerfCode';
  String? token = await storage.read(key: 'token');

  if (token == null) {
    throw Exception('Authentication token not found');
  }

  try {
    final body = jsonEncode({
      'CustID': customerId,
    });

    final response = await http
        .post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: body,
        )
        .timeout(
          const Duration(seconds: 7),
          onTimeout: () {
            return http.Response(
              jsonEncode({'error': 'timeout'}),
              408,
            );
          },
        );

    if (response.statusCode == 403) {
      await refreshToken();
      throw Exception('Authentication failed');
    }

    if (response.statusCode == 200) {
      return jsonDecode(response.body).toString();
    } else {
      throw Exception('Failed to generate verification code. Status code: ${response.statusCode}');
    }
  } catch (e) {
    throw Exception('Failed to generate verification code: $e');
  }
}

// Helper function to get orders list for backward compatibility
Future<List<OrderResponse>> getOrdersList({
  int page = 1,
  int limit = 10,
  String search = '',
  int? isDelivered,
}) async {
  try {
    final result = await GetOrders(
      page: page,
      limit: limit,
      search: search,
      isDelivered: isDelivered,
    );

    if (result.success) {
      return result.data.orders;
    } else {
      // Return error response in the expected format
      return [
        OrderResponse(
          orderId: 0,
          orderNum: 'ERROR',
          numPackages: 0,
          numCbm: 0,
          priceLD: 0.0,
          totePriceLD: 0.0,
          orderTypeId: 0,
          isDelivered: false,
          status: result.msg,
          orderType: '',
          unit: '',
          statusNum: 0,
        ),
      ];
    }
  } catch (e) {
    // Return error response in the expected format
    return [
      OrderResponse(
        orderId: 0,
        orderNum: 'ERROR',
        numPackages: 0,
        numCbm: 0,
        priceLD: 0.0,
        totePriceLD: 0.0,
        orderTypeId: 0,
        isDelivered: false,
        status: 'خطأ في الاتصال',
        orderType: '',
        unit: '',
        statusNum: 0,
      ),
    ];
  }
}
