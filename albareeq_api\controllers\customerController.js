var initModels = require("../models/init-models").initModels;
const sequelize = require('../dbConfigrtion/sequelizeDb');
const { json } = require("body-parser");
const moment = require('moment');
const { getDigitalCode } = require('node-verification-code')
var models = initModels(sequelize);

const NewCusVerfCode = async (req, res) => {
  ID=req.body.CustID
  const VerfCode = getDigitalCode(4)
  await models.customer.update({
    VirfCode : VerfCode
  },
    {
      where: {
        ID: ID,
      },

    })
  res.status(200).json(VerfCode)

}


const UpdateCus = async (req, res) => {
  const mainObj = req.body;
  var d = moment().format('DD-MM-YYYY hh:mm:ss');

  try {
    if (mainObj.Password && mainObj.Password.trim().length <= 3) {
      return res.status(400).json({ error: "Password must be more than 3 characters" });
    }
    const updateObj = {
      UpdateDate: sequelize.literal(`convert(datetime,'${d}', 103)`),
      UpdatedBy: mainObj.UserID
    };

    if (mainObj.CustName && mainObj.CustName.trim() !== "") {
      updateObj.CustName = mainObj.CustName.trim();
    }

    if (mainObj.PhoneNum1 && mainObj.PhoneNum1.trim() !== "") {
      updateObj.PhoneNum1 = mainObj.PhoneNum1.trim();
    }

    if (mainObj.PhoneNum2 !== null && mainObj.PhoneNum2 !== undefined) {
      updateObj.PhoneNum2 = mainObj.PhoneNum2.trim();
    }

    if (mainObj.Address !== null && mainObj.Address !== undefined) {
      updateObj.Address = mainObj.Address.trim();
    }

    if (mainObj.Password && mainObj.Password.trim() !== "") {
      updateObj.Password = mainObj.Password.trim();
    }

    if (mainObj.Email !== null && mainObj.Email !== undefined) {
      updateObj.Email = mainObj.Email.trim();
    }

    const or = await models.customer.update(
      updateObj,
      {
        where: {
          ID: mainObj.ID,
        },
      }
    );

    if (or[0] === 0) {
      return res.status(404).json({ error: "Customer not found" });
    }

    // Update customer ID format
    await models.customer.update({
      cus_Id: 'Cus' + mainObj.ID.toString()
    },
      {
        where: {
          ID: mainObj.ID,
        },
      });

    return res.status(201).json({ message: "Customer updated successfully" });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
}

const AddCust = async (req, res) => {
  const mainObj = req.body;
   var d = moment().format('DD-MM-YYYY hh:mm:ss');
  var or = models.customer.build(
    {
      cus_Id: 'Cus' ,
      CustName: mainObj.CustName,
      PhoneNum1: mainObj.PhoneNum1,
      PhoneNum2: mainObj.PhoneNum2,
      Address: mainObj.Address,
      IntialPlance: 0,
      Iscollred: false,
      IntialPlanceAir:0,
      UserName: mainObj.UserName,
      Password:mainObj.Password,
      Email:mainObj.Email,
      VirfCode: getDigitalCode(4),
      Status: 1,
      InsertDate: sequelize.literal(`convert(datetime,'${d}', 103)`),
      InsertedBy: mainObj.UserID
    }
  );
  await or.save();
  console.log(or.dataValues.ID)
  await models.customer.update({
    cus_Id : 'Cus' +  or.dataValues.ID.toString() 
  },
    {
      where: {
        ID: or.dataValues.ID,
      },

    })
  return res.status(201).json("accepted")
}

const GetbyID = async (req, res) => {
    ID=req.body.ID
    var Result = await models.customer.findAll({
      attributes: [
        "ID", "CustName", "PhoneNum1", "PhoneNum2", "Address", "UserName", "Password", "Email", "Status", "cus_Id"
      ],
      where: {
        ID: ID,
      },
      raw: true,
      nest: true
    }).catch(err => {
      console.log(err)
      res.status(500).json({ msg: " Internal Server Error" })
    })
    res.status(200).json(Result)
  
  }

  module.exports = {
    GetbyID ,
    AddCust,
    UpdateCus,
    NewCusVerfCode,
  }
  ///////////////