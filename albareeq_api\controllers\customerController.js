var initModels = require("../models/init-models").initModels;
const sequelize = require('../dbConfigrtion/sequelizeDb');
const { json } = require("body-parser");
const moment = require('moment');
const { getDigitalCode } = require('node-verification-code')
var models = initModels(sequelize);

const NewCusVerfCode = async (req, res) => {
  ID=req.body.CustID
  const VerfCode = getDigitalCode(4)
  await models.customer.update({
    VirfCode : VerfCode
  },
    {
      where: {
        ID: ID,
      },

    })
  res.status(200).json(VerfCode)

}


const UpdateCus = async (req, res) => {
  const mainObj = req.body;
  var d = moment().format('DD-MM-YYYY hh:mm:ss');
  try {
    const or = await models.customer.update(
      {
        CustName: mainObj.CustName,
        PhoneNum1: mainObj.PhoneNum1,
        PhoneNum2: mainObj.PhoneNum2,
        Address: mainObj.Address,
        Password: mainObj.Password,
        Email: mainObj.Email,
        UpdateDate: sequelize.literal(`convert(datetime,'${d}', 103)`),
        UpdatedBy: mainObj.UserID
      },
      {
        where: {
          ID: mainObj.ID,
        },
      }
    );
    if (or[0] === 0) {
      return res.status(404).json("Customer not found");
    }
    await models.customer.update({
      cus_Id: 'Cus' + mainObj.ID.toString()
    },
      {
        where: {
          ID: mainObj.ID,
        },
      });
    return res.status(201).json("accepted");
  } catch (error) {
    console.error(error);
    return res.status(500).json("Internal Server Error");
  }
}

const AddCust = async (req, res) => {
  const mainObj = req.body;
   var d = moment().format('DD-MM-YYYY hh:mm:ss');
  var or = models.customer.build(
    {
      cus_Id: 'Cus' ,
      CustName: mainObj.CustName,
      PhoneNum1: mainObj.PhoneNum1,
      PhoneNum2: mainObj.PhoneNum2,
      Address: mainObj.Address,
      IntialPlance: 0,
      Iscollred: false,
      IntialPlanceAir:0,
      UserName: mainObj.UserName,
      Password:mainObj.Password,
      Email:mainObj.Email,
      VirfCode: getDigitalCode(4),
      Status: 1,
      InsertDate: sequelize.literal(`convert(datetime,'${d}', 103)`),
      InsertedBy: mainObj.UserID
    }
  );
  await or.save();
  console.log(or.dataValues.ID)
  await models.customer.update({
    cus_Id : 'Cus' +  or.dataValues.ID.toString() 
  },
    {
      where: {
        ID: or.dataValues.ID,
      },

    })
  return res.status(201).json("accepted")
}

const GetbyID = async (req, res) => {
    ID=req.body.ID
    var Result = await models.customer.findAll({
      attributes: [
        "ID", "CustName", "PhoneNum1", "PhoneNum2", "Address", "UserName", "Password", "Email", "Status", "cus_Id"
      ],
      where: {
        ID: ID,
      },
      raw: true,
      nest: true
    }).catch(err => {
      console.log(err)
      res.status(500).json({ msg: " Internal Server Error" })
    })
    res.status(200).json(Result)
  
  }

  module.exports = {
    GetbyID ,
    AddCust,
    UpdateCus,
    NewCusVerfCode,
  }
  ///////////////