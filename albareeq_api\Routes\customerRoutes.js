const express = require("express")
const router = express.Router();
const customerController = require("../controllers/customerController")
const JwtVerify = require("../middleware/verifyJWT");

router.use(JwtVerify);

router.route("/getbyID").post(customerController.GetbyID);
router.route("/AddCust").post(customerController.AddCust);
router.route("/UpdateCus").post(customerController.UpdateCus);
router.route("/NewCusVerfCode").post(customerController.NewCusVerfCode);



module.exports = router
