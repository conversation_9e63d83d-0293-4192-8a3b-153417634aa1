const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('customer', {
    ID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    cus_Id: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    CustName: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    PhoneNum1: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    Email: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    VirfCode: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    PhoneNum2: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    Address: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    CpmPrisc: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    Status: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    InsertedBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    InsertDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    UpdatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    UpdateDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    IntialPlance: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    Iscollred: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    IsMobile1Verf: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    IsMobile2Verf: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    IsEmailVir: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    IntialPlanceAir: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    UserName: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    Password: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    CanLogIn: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    LastLoginDate: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'customer',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK_customer",
        unique: true,
        fields: [
          { name: "ID" },
        ]
      },
    ]
  });
};
