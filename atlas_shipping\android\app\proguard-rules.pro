# Keep classes used by google-http-client and joda-time
-keep class com.google.api.** { *; }
-keep class com.google.http.** { *; }
-keep class org.joda.** { *; }
-keep class org.apache.http.** { *; }
-keep interface org.apache.http.** { *; }

# Handle javax annotations
-keep class javax.annotation.** { *; }
-dontwarn javax.annotation.**

# Ignore missing error-prone annotations
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.checkerframework.**

# Avoid R8 failure from missing annotations (we don't need them at runtime)
-dontwarn javax.lang.model.**

# Disable warnings for apache httpclient (if accidentally pulled)
-dontwarn org.apache.http.**
-dontwarn org.apache.http.client.methods.HttpEntityEnclosingRequestBase
-dontwarn org.apache.http.client.methods.HttpRequestBase
-dontwarn org.apache.http.client.methods.HttpUriRequest
-dontwarn org.apache.http.HttpEntityEnclosingRequest
-dontwarn org.apache.http.HttpRequest
-dontwarn org.apache.http.message.AbstractHttpMessage

# Needed for joda-time convert annotations
-dontwarn org.joda.convert.**
-keep class org.joda.convert.** { *; }

# General
-dontnote
-dontwarn
